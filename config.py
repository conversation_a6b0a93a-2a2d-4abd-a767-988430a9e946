# 每批次最多的三级模块数量
BATCH_COUNT = 30
#LEVEL2_NAME = "密码资产数据管理"
LEVEL2_NAME = "密码资产数据管理"
# ENDPOINT_URL="http://10.10.43.64:3300/v1/chat/completions"
# #ENDPOINT_URL="https://ai.secsign.online:3003/v1/chat/completions"
# MODEL_NAME="qwen3-32b"
# API_KEY="sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB"

# 阿里云
ENDPOINT_URL="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
## ENDPOINT_URL="https://10.10.43.64:3000/compatible-mode/v1/chat/completions"
MODEL_NAME="qwen3-235b-a22b-instruct-2507"
API_KEY="sk-1d06e75d7fd94338b5b32cf8f9099651"
# 每分钟的调用次数限制
API_QPM=60
# 每分钟处理的文本token数量
API_TPM=100000

# openrouter
# ENDPOINT_URL="https://openrouter.ai/api/v1/chat/completions"
# MODEL_NAME="moonshotai/kimi-k2:free"
# API_KEY="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"
# # 每分钟的调用次数限制
# API_QPM=1
# # 每分钟处理的文本token数量
# API_TPM=1000000


# 验证CFP拆分的4个检查点
## 1.完整性检查
# 每个功能过程是否包含 至少1个E和1个X？
# *例：密码方案收集（1E+1R+2W+1X）→ 通过*
# ## 2.数据组聚合检查
# 是否将关联数据属性 合并为最小单元？
# *例：用户姓名+电话 → 1个数据组，非2个*
# ## 3.存储边界检查
# R/W是否仅针对 边界内持久存储？
# 例：读取外部医保API → 不计R，应计E（输入）
# ## 4.无重复计数
# 同一数据组在同一功能过程中 是否被重复计数？
# 例：读取用户信息后再次读取 → 计2次R