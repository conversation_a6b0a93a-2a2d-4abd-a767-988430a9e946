﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP,data_attributes,data_group,data_move_type,data属性
密码资产数据管理,密码资产数据管理,密码服务数据库新增,新增数据库信息，选择数据库类型，输入数据库IP、端口，管理员账号、密码,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在数据库管理页面点击新增按钮，进入新增数据库表单页面,录入并保存新的密码服务数据库信息,输入数据库类型,E,数据库基本信息,数据库类型,1,,,,
,,,,,,,,输入数据库IP和端口,E,数据库连接信息,数据库IP、端口,1,,,,
,,,,,,,,输入管理员账号和密码,E,数据库认证信息,管理员账号、密码,1,,,,
,,,,,,,,提交数据库配置信息,W,密码服务数据库记录,数据库类型、IP、端口、账号、密码,1,,,,
,,密码服务数据库列表,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入密码服务数据库列表页面,分页展示所有已配置的密码服务数据库信息,请求数据库列表分页参数,E,分页信息,页码、每页数量,1,,,,
,,,,,,,,读取数据库记录列表,R,密码服务数据库记录,数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验状态,1,,,,
,,,,,,,,返回数据库列表数据,X,数据库列表展示数据,,1,数据库名称、类型、实例库、IP端口、校验状态,,,
,,密码服务数据库模式列表,列表展示密码服务数据库模式,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入数据库模式管理页面,展示所有已配置的数据库模式,请求数据库模式列表分页参数,E,分页信息,,1,页码、每页数量,,,
,,,,,,,,读取数据库模式记录,R,数据库模式信息,,1,模式名称、关联数据库、创建时间,,,
,,,,,,,,返回数据库模式列表,X,,,1,模式名称、数据库、创建时间,数据库模式列表展示数据,,
,,密码服务数据库模式删除,删除密码服务数据库模式,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在数据库模式列表中选择某模式并点击删除按钮,删除指定的数据库模式,选择待删除的数据库模式,E,,,1,模式ID,数据库模式选择,,
,,,,,,,,验证模式是否可删除（无依赖）,,,,1,关联服务数量,数据库模式依赖检查,R,
,,,,,,,,执行数据库模式删除操作,,,,1,模式ID、状态标记为已删除,数据库模式信息,W,
,,密码服务数据库模式查询,查询密码服务数据库模式,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在数据库模式页面输入查询条件并点击搜索,根据条件查询数据库模式,输入查询条件,,,,1,模式名称、数据库类型,查询条件,E,
,,,,,,,,执行数据库模式匹配查询,,,,1,模式名称、数据库类型、创建时间,数据库模式信息,R,
,,,,,,,,返回查询结果列表,,,,1,匹配的模式列表,查询结果数据,X,
,,密码服务数据库模式新增,新增数据库模式,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员点击新增数据库模式按钮,新增一个数据库模式配置,输入数据库模式名称,,,,1,模式名称,数据库模式信息,E,
,,,,,,,,选择关联的数据库实例,,,,1,数据库ID,数据库关联信息,E,
,,,,,,,,保存数据库模式配置,,,,1,模式名称、数据库ID、创建时间,数据库模式信息,W,
,,API网关列表,列表内容：名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP（列表不显示）、端口（列表不显示）、反向代理地址端口（列表不显示）,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入API网关管理页面,分页展示所有API网关实例信息,请求分页参数,,,,1,页码、每页数量,分页信息,E,
,,,,,,,,读取API网关记录,,,,1,名称、所属区域、标识、类型、IP、业务端口、管理端口,API网关信息,R,
,,,,,,,,返回网关列表展示数据,,,,1,名称、区域、标识、类型、IP、端口,API网关列表数据,X,
,,API网关初始化,密码服务平台部署成功后，如选择部署API网关，根据平台部署信息，自动加载对应的部署网关信息,3.0,发起者：系统部署模块，接收者：密码资产数据管理系统,密码服务平台部署完成且选择部署API网关,自动加载并初始化API网关配置信息,检测平台部署配置中是否启用API网关,,,,1,是否启用API网关,平台部署配置,R,
,,,,,,,,读取部署环境中的网关信息,,,,1,网关IP、端口、类型,部署环境信息,R,
,,,,,,,,初始化并写入默认API网关记录,,,,1,名称、IP、管理端口、类型、状态,API网关信息,W,
,,API网关新增,新增API网关信息，录入名称、所属需求、标识、类型（管理、业务）、管理端口,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在API网关页面点击新增按钮并提交表单,新增一个API网关实例,输入网关名称和标识,,,,1,名称、标识,网关基本信息,E,
,,,,,,,,选择所属区域和类型,,,,1,所属区域、类型（管理/业务）,网关分类信息,E,
,,,,,,,,输入管理端口,,,,1,管理端口,网关连接信息,E,
,,,,,,,,保存API网关配置,,,,1,名称、区域、标识、类型、管理端口,API网关信息,W,
,,API网关编辑,编辑内容：网关名称、管理端口,nan,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在API网关列表中选择某网关并点击编辑按钮,修改API网关的基本信息,选择待编辑的API网关,,,,1,网关ID,网关选择,E,
,,,,,,,,读取当前网关配置,,,,1,当前名称、管理端口,API网关信息,R,
,,,,,,,,输入新的网关名称或管理端口,,,,1,新名称、新管理端口,修改信息,E,
,,,,,,,,更新网关配置信息,,,,1,更新后的名称、管理端口,API网关信息,W,
,,API网关删除,删除网关信息,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在API网关列表中选择某网关并点击删除按钮,删除指定的API网关记录,选择待删除的API网关,,,,1,网关ID,网关选择,E,
,,,,,,,,检查网关是否被路由引用,,,,1,引用数量,路由依赖检查,R,
,,,,,,,,执行网关删除操作,,,,1,网关ID、状态标记为已删除,API网关信息,W,
,,路由管理列表,展示内容：路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入路由管理页面,分页展示所有路由配置,请求分页参数,,,,1,页码、每页数量,分页信息,E,
,,,,,,,,读取路由配置列表,,,,1,路由名称、组件标识、服务类型、应用、服务组、URL路径、上游配置、匹配条件、超时时间,路由信息,R,
,,,,,,,,返回路由展示数据,,,,1,路由名称、服务类型、URL路径、超时时间,路由列表数据,X,
,,路由管理详情,展示路由管理详情，包含服务列表信息、应用信息,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在路由列表中点击某路由的详情按钮,查看路由的详细配置信息,选择目标路由,,,,1,路由ID,路由选择,E,
,,,,,,,,读取路由完整配置,,,,1,路由名称、组件标识、服务类型、应用、服务组、URL路径、上游配置、匹配条件、超时时间,路由信息,R,
,,,,,,,,读取关联的服务列表信息,,,,1,服务名称、IP、端口,服务列表信息,R,
,,,,,,,,读取所属应用信息,,,,1,应用名称、负责人、环境,应用信息,R,
,,,,,,,,返回路由详情展示数据,,,,1,完整路由与关联信息,路由详情数据,X,
,,设备类型展示,内容：设备类型名称、所属厂商（录入）、设备类型（云密码机、物理密码机、虚拟密码机）、管理接口协同（HTTPS、HTTP）、管理端口；云密码机、虚拟密码机、物理密码机类型配置信息不同,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入设备类型管理页面,展示所有设备类型的配置信息,请求设备类型列表分页参数,,,,1,页码、每页数量,分页信息,E,
,,,,,,,,读取设备类型记录,,,,1,设备类型名称、所属厂商、设备类型（云/物理/虚拟）、管理接口协议、管理端口,设备类型信息,R,
,,,,,,,,返回设备类型展示数据,,,,1,名称、厂商、类型、协议、端口,设备类型列表数据,X,
,,设备类型初始化,根据平台支持的设备类型，平台部署时，初始化平台默认支持的设备类型,4.0,发起者：系统部署模块，接收者：密码资产数据管理系统,平台部署完成，初始化设备类型配置,加载平台默认支持的设备类型,读取平台支持的设备类型清单,,,,1,设备类型名称、默认配置模板,平台设备类型定义,R,
,,,,,,,,检查数据库中是否已存在该类型,,,,1,类型名称,设备类型信息,R,
,,,,,,,,写入未存在的默认设备类型,,,,1,名称、厂商、类型、协议、端口,设备类型信息,W,
,,设备类型新增,添加设备对应信息,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备类型页面点击新增按钮并提交表单,添加新的设备类型配置,输入设备类型名称和厂商,,,,1,名称、厂商,设备类型基本信息,E,
,,,,,,,,选择设备分类（云/物理/虚拟）,,,,1,设备类型,设备分类信息,E,
,,,,,,,,配置管理接口协议和端口,,,,1,协议（HTTPS/HTTP）、端口,管理接口配置,E,
,,,,,,,,保存设备类型配置,,,,1,完整设备类型配置,设备类型信息,W,
,,设备类型编辑,编辑设备相关信息,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备类型列表中选择某类型并点击编辑按钮,修改设备类型的配置信息,选择待编辑的设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,读取当前设备类型配置,,,,1,当前所有属性,设备类型信息,R,
,,,,,,,,修改设备类型字段,,,,1,名称、厂商、协议等,修改信息,E,
,,,,,,,,更新设备类型配置,,,,1,更新后的配置,设备类型信息,W,
,,设备类型停用,停用设备类型不可再创建该类型的设备,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备类型列表中点击停用按钮,停用指定设备类型，禁止新建设备,选择待停用的设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,检查是否存在该类型的活跃设备,,,,1,设备数量,设备实例信息,R,
,,,,,,,,更新设备类型状态为停用,,,,1,状态字段设为停用,设备类型信息,W,
,,设备类型启用,启用停用的设备类型,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备类型列表中对已停用类型点击启用按钮,重新启用已停用的设备类型,选择待启用的设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,读取设备类型当前状态,,,,1,当前状态,设备类型信息,R,
,,,,,,,,更新设备类型状态为启用,,,,1,状态字段设为启用,设备类型信息,W,
,,设备类型删除,当无该类型的设备时，删除对应设备类型,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备类型列表中选择某类型并点击删除按钮,删除设备类型定义,选择待删除的设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,检查是否存在该类型的设备实例,,,,1,实例数量,设备实例信息,R,
,,,,,,,,执行设备类型删除操作,,,,1,类型ID、记录标记为删除,设备类型信息,W,
,,监控信息配置查看,查询当前监控信息的配置信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入设备类型监控配置页面,查看当前设备类型的监控方式配置,选择目标设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,读取该类型的监控配置,,,,1,SNMP配置、Rest接口配置、监控组件、探针配置,监控配置信息,R,
,,,,,,,,返回监控配置展示数据,,,,1,各监控方式参数,监控配置展示信息,X,
,,监控信息配置,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在监控配置页面设置监控参数并提交,配置设备类型的监控方式,选择设备类型,,,,1,类型ID,设备类型选择,E,
,,,,,,,,选择监控方式（SNMP/Rest/组件/探针）,,,,1,监控类型,监控方式选择,E,
,,,,,,,,输入对应监控参数,,,,1,IP、端口、OID、认证信息等,监控参数,E,
,,,,,,,,保存监控配置,,,,1,完整监控参数,监控配置信息,W,
,,密码设备集群列表,列表内容：名称、设备类型、所属区域、设备数量、描述,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员进入密码设备集群管理页面,分页展示所有设备集群信息,请求分页参数,,,,1,页码、每页数量,分页信息,E,
,,,,,,,,读取设备集群记录,,,,1,名称、设备类型、所属区域、设备数量、描述,密码设备集群信息,R,
,,,,,,,,返回集群列表展示数据,,,,1,名称、类型、区域、数量,集群列表数据,X,
,,密码设备集群新增,创建密码设备集群，录入名称、选择设备类型、所属区域、描述,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员点击新增密码设备集群按钮并提交,创建新的密码设备集群,输入集群名称,,,,1,名称,集群基本信息,E,
,,,,,,,,选择设备类型和所属区域,,,,1,设备类型、区域,集群分类信息,E,
,,,,,,,,输入集群描述,,,,1,描述,集群描述信息,E,
,,,,,,,,保存集群配置,,,,1,名称、类型、区域、描述,密码设备集群信息,W,
,,密码设备集群编辑,可编辑内容：名称、描述,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在集群列表中选择某集群并点击编辑按钮,修改密码设备集群的基本信息,选择待编辑的集群,,,,1,集群ID,集群选择,E,
,,,,,,,,读取当前集群信息,,,,1,当前名称、描述,密码设备集群信息,R,
,,,,,,,,输入新的名称或描述,,,,1,新名称、新描述,修改信息,E,
,,,,,,,,更新集群信息,,,,1,更新后的字段,密码设备集群信息,W,
,,密码设备集群删除,删除密码设备集群，需保障密码设备集群未被密码服务调用,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在集群列表中选择某集群并点击删除按钮,删除密码设备集群,选择待删除的集群,,,,1,集群ID,集群选择,E,
,,,,,,,,检查集群是否被密码服务调用,,,,1,调用数量,服务调用关系,R,
,,,,,,,,执行集群删除操作,,,,1,状态标记为已删除,密码设备集群信息,W,
,,绑定密码设备,根据密码设备类型绑定密码设备，绑定密码设备后，根据类型配置判断是否需要进行保护密钥的创建和同步,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备管理页面执行绑定操作,将密码设备绑定到设备集群，并根据类型判断是否创建保护密钥,选择目标设备和集群,,,,1,设备ID、集群ID,绑定关系输入,E,
,,,,,,,,读取设备类型配置,,,,1,是否需要保护密钥,设备类型信息,R,
,,,,,,,,创建设备与集群的绑定关系,,,,1,设备ID、集群ID、绑定时间,设备绑定关系,W,
,,,,,,,,如需保护密钥，则生成并同步密钥,,,,1,密钥ID、密钥值、同步状态,保护密钥信息,W,
,,释放密码设备,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在设备绑定列表中点击释放按钮,解除密码设备与集群的绑定关系,选择待释放的设备绑定关系,,,,1,绑定ID,绑定关系选择,E,
,,,,,,,,检查是否为最后一个设备,,,,1,当前设备数,集群设备数量,R,
,,,,,,,,如为最后一个，检查集群是否被服务调用,,,,1,调用状态,服务调用关系,R,
,,,,,,,,解除设备绑定关系,,,,1,状态设为已释放,设备绑定关系,W,
,,云密码机列表,云密码机列表页面中，在搜索框中输入名称和管理IP，可以模糊查询云密码机列表,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在云密码机页面输入搜索条件并提交,模糊查询并展示云密码机列表,输入搜索关键词（名称或管理IP）,,,,1,名称、管理IP,查询条件,E,
,,,,,,,,执行模糊匹配查询,,,,1,名称、管理IP、状态、区域,云密码机信息,R,
,,,,,,,,返回匹配的云密码机列表,,,,1,名称、IP、状态,查询结果列表,X,
,,云密码机新建,云密码机管理页面中，点击“新建”按钮，打开添加云密码机页面。输入云密码机信息，点击“确定”按钮添加云密码机。管理ip和管理端口根据实际设备部署情况获取,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,管理员在云密码机页面点击新建按钮并提交表单,添加新的云密码机设备,输入云密码机名称,,,,1,名称,设备基本信息,E,
,,,,,,,,输入管理IP和端口,,,,1,管理IP、管理端口,网络配置,E,
,,,,,,,,选择设备类型和所属区域,,,,1,设备类型、区域,分类信息,E,
,,,,,,,,读取设备实际部署参数,,,,1,IP、端口,部署环境信息,R,
,,,,,,,,保存云密码机记录,,,,1,名称、IP、端口、类型、区域,云密码机信息,W,
,,云密码机编辑,云密码机信息列表，点击右侧操作列“编辑”按钮，打开编辑云密码机信息页面，修改云密码机名称和备注，点击“确定”按钮保存云密码机信息。,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户在云密码机列表页点击操作列的“编辑”按钮,加载并展示云密码机编辑页面,读取当前云密码机基本信息,R,云密码机信息,云密码机名称、备注,1,,,,
,,,,,,,,输入修改后的云密码机信息,E,云密码机信息,云密码机名称、备注,1,,,,
,,,,,,,,更新云密码机信息至数据库,W,云密码机信息,云密码机名称、备注,1,,,,
,,云密码机删除,云密码机列表页，点击右侧操作列 更多->“删除”按钮，在弹出框中点击“确定”按钮删除云密码机。系统中存在使用该云密码机生成的虚拟机时，云密码机不能删除。,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户在云密码机列表点击“更多->删除”按钮,检查云密码机是否可删除并执行删除操作,查询该云密码机关联的虚拟密码机,R,虚拟密码机列表,虚拟密码机ID、名称、所属云密码机,1,,,,
,,,,,,,,判断是否存在正在使用的虚拟密码机,R,使用状态信息,虚拟密码机使用状态,1,,,,
,,,,,,,,若无可删除虚拟机，则删除云密码机记录,W,云密码机信息,,1,,,,云密码机ID
,,云密码机详情,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击云密码机列表“详情”按钮,展示云密码机详细信息,输入云密码机ID,E,云密码机标识,云密码机ID,1,,,,
,,,,,,,,读取云密码机完整信息,R,云密码机信息,名称、IP、端口、厂商、型号、备注、创建时间,1,,,,
,,,,,,,,输出云密码机详情页面,X,云密码机详情,所有字段信息,1,,,,
,,网络配置列表,查看为云密码机配置的虚机网络,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户访问网络配置列表页面,展示云密码机的虚拟机网络配置,请求网络配置列表,E,分页信息,页码、每页数量,1,,,,
,,,,,,,,读取虚拟机网络配置数据,R,虚拟机网络配置,管理IP范围、业务IP范围、子网掩码、网关,1,,,,
,,,,,,,,展示网络配置列表,X,网络配置列表,所有网络配置项,1,,,,
,,新增虚拟机网络配置,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户提交新增虚拟机网络配置表单,创建新的虚拟机网络配置,输入管理IP范围和业务IP范围,E,虚拟机网络配置,管理IP范围、业务IP范围,1,,,,
,,,,,,,,验证IP范围格式与可用性,R,IP段校验规则,IP格式、冲突检测,1,,,,
,,,,,,,,保存新的网络配置到数据库,W,虚拟机网络配置,管理IP范围、业务IP范围、创建时间,1,,,,
,,批量创建虚拟机,批量创建虚拟密码机，自动加载虚机网络，支持配置虚机资源。调用云密码机0088标准创建虚机并自动配置网络。,10.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户执行批量创建虚拟密码机操作,批量创建虚拟密码机并自动配置网络,选择目标云密码机,E,云密码机选择,云密码机ID,1,,,,
,,,,,,,,读取预设的虚拟机网络配置,R,虚拟机网络配置,管理IP范围、业务IP范围,1,,,,
,,,,,,,,输入批量创建参数（数量、资源规格）,E,创建参数,数量、CPU、内存、磁盘,1,,,,
,,,,,,,,调用0088标准接口创建虚拟机,W,虚拟密码机实例,实例ID、名称、IP、状态,1,,,,
,,,,,,,,自动分配IP并配置网络,W,网络分配记录,分配IP、端口、绑定关系,1,,,,
,,,,,,,,返回创建结果,X,创建结果,成功数量、失败列表,1,,,,
,,虚拟密码机列表,平台中云机虚拟出的VSM,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户进入虚拟密码机列表页面,展示所有虚拟密码机列表,请求虚拟密码机列表,E,分页信息,页码、每页数量,1,,,,
,,,,,,,,读取虚拟密码机数据,R,虚拟密码机列表,名称、主机、管理IP、服务IP、设备类型、状态,1,,,,
,,,,,,,,输出虚拟密码机列表,X,虚拟密码机列表,所有字段信息,1,,,,
,,虚拟密码机列表查询,支持根据名称、主机、管理ip、服务ip、设备类型进行查询,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户输入查询条件并提交,根据条件查询虚拟密码机,输入查询条件,E,查询条件,名称、主机、管理IP、服务IP、设备类型,1,,,,
,,,,,,,,执行条件匹配查询,R,虚拟密码机列表,名称、主机、管理IP、服务IP、设备类型,1,,,,
,,,,,,,,返回查询结果,X,查询结果,匹配的虚拟密码机列表,1,,,,
,,创建虚拟密码机,选择云密码机，批量创建虚拟密码机，和云机管理中批量创建密码机一致,2.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户选择云密码机并执行创建虚拟密码机操作,创建单个或多个虚拟密码机,选择目标云密码机,E,云密码机选择,云密码机ID,1,,,,
,,,,,,,,读取可用网络配置,R,虚拟机网络配置,IP范围、端口池,1,,,,
,,,,,,,,生成虚拟机配置参数,E,虚拟机配置,名称、资源规格,1,,,,
,,,,,,,,保存虚拟机实例信息,W,虚拟密码机实例,名称、IP、状态、所属云机,1,,,,
,,虚拟密码机详情,虚拟密码机详情,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击虚拟密码机“详情”按钮,展示虚拟密码机详细信息,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,读取虚拟密码机完整信息,R,虚拟密码机信息,名称、管理IP、服务IP、设备类型、状态、资源规格、创建时间,1,,,,
,,,,,,,,输出详情页面,X,虚拟密码机详情,所有字段信息,1,,,,
,,编辑虚拟密码机,编辑虚拟密码机名称、连接密码，并在动态下发给密码服务,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户修改虚拟密码机名称或连接密码并点击保存,更新虚拟密码机信息并动态下发至密码服务,读取当前虚拟密码机信息,R,虚拟密码机信息,名称、连接密码,1,,,,
,,,,,,,,输入修改后的名称和连接密码,E,修改信息,名称、连接密码,1,,,,
,,,,,,,,更新数据库中的虚拟密码机信息,W,虚拟密码机信息,名称、连接密码,1,,,,
,,,,,,,,将更新信息动态下发至密码服务,W,密码服务配置,连接密码、服务标识,1,,,,
,,删除虚拟密码机,删除虚拟密码机,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击“删除”按钮确认删除虚拟密码机,删除虚拟密码机记录,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,检查虚拟机当前状态,R,虚拟密码机状态,运行状态,1,,,,
,,,,,,,,从数据库中删除虚拟密码机记录,W,虚拟密码机信息,虚拟密码机ID,1,,,,
,,启动虚拟密码机,启动虚拟密码机,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击“启动”按钮,启动指定虚拟密码机,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,读取虚拟机当前状态,R,虚拟密码机状态,当前状态,1,,,,
,,,,,,,,发送启动指令至虚拟机,W,控制指令,启动命令、目标ID,1,,,,
,,,,,,,,更新虚拟机状态为运行中,W,虚拟密码机状态,状态=运行中,1,,,,
,,停止虚拟密码机,停止虚拟密码机,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击“停止”按钮,停止指定虚拟密码机,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,读取虚拟机当前状态,R,虚拟密码机状态,当前状态,1,,,,
,,,,,,,,发送停止指令至虚拟机,W,控制指令,停止命令、目标ID,1,,,,
,,,,,,,,更新虚拟机状态为已停止,W,虚拟密码机状态,状态=已停止,1,,,,
,,重启虚拟密码机,重启虚拟密码机,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击“重启”按钮,重启指定虚拟密码机,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,读取虚拟机当前状态,R,虚拟密码机状态,当前状态,1,,,,
,,,,,,,,发送重启指令至虚拟机,W,控制指令,重启命令、目标ID,1,,,,
,,,,,,,,更新虚拟机状态为重启中,W,虚拟密码机状态,状态=重启中,1,,,,
,,强制删除虚拟密码机,解决虚拟密码机已不存在，无法正常删除情况,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户对已不存在的虚拟密码机执行强制删除,清除系统中残留的虚拟密码机记录,输入虚拟密码机ID,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,验证该虚拟机是否已无法通信,R,连接状态,心跳状态、连接超时,1,,,,
,,,,,,,,强制删除数据库中的记录,W,虚拟密码机信息,虚拟密码机ID,1,,,,
,,生成虚机影像,生成虚机影像,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户执行生成虚机影像操作,生成虚拟密码机的系统镜像,选择目标虚拟密码机,E,虚拟密码机标识,虚拟密码机ID,1,,,,
,,,,,,,,读取虚拟机当前运行状态,R,虚拟密码机状态,运行状态,1,,,,
,,,,,,,,执行镜像生成命令,W,镜像生成任务,任务ID、目标ID、时间戳,1,,,,
,,,,,,,,保存镜像元数据,W,虚机影像信息,影像名称、大小、创建时间,1,,,,
,,下载虚机影像,下载虚机影像,4.0,发起者：系统管理员，接收者：本地终端,用户点击“下载”按钮,将虚机影像文件下载到本地,选择要下载的虚机影像,E,虚机影像标识,影像ID,1,,,,
,,,,,,,,读取影像文件存储路径,R,影像存储信息,存储路径、文件名,1,,,,
,,,,,,,,发起文件下载流,X,虚机影像文件,二进制流,1,,,,
,,导入虚机影像,导入虚机影像，还原虚机影像,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户上传虚机影像文件并确认导入,导入并还原虚机影像,上传虚机影像文件,E,虚机影像文件,文件二进制流,1,,,,
,,,,,,,,解析影像文件元数据,R,影像元数据,名称、版本、校验码,1,,,,
,,,,,,,,保存影像至存储目录,W,虚机影像信息,存储路径、大小、创建时间,1,,,,
,,,,,,,,注册影像到系统,W,影像注册表,影像ID、状态,1,,,,
,,物理密码机列表,物理密码机列表展示已经注册到系统中物理密码机信息，包含密码机的名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注等信息。,4.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户进入物理密码机列表页面,展示所有已注册的物理密码机信息,请求物理密码机列表,E,分页信息,页码、每页数量,1,,,,
,,,,,,,,读取物理密码机数据,R,物理密码机列表,名称、厂商、设备类型、管理IP、端口、序列号、版本、备注,1,,,,
,,,,,,,,输出物理密码机列表,X,物理密码机列表,所有字段信息,1,,,,
,,物理密码机新建,系统密码机是将机房已经上架部署完毕的密码机注册到系统中，交由管理平台进行统一管理。,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户提交物理密码机注册表单,将物理密码机注册到系统中,输入物理密码机基本信息,E,物理密码机信息,名称、厂商、设备类型、管理IP、端口、序列号,1,,,,
,,,,,,,,验证设备连接性,R,连接测试结果,心跳响应、认证状态,1,,,,
,,,,,,,,保存设备信息至数据库,W,物理密码机信息,所有字段,1,,,,
,,物理密码机编辑,在密码机列表页，点击右侧操作列“编辑”按钮，打开物理密码机编辑页面，修改物理密码机信息(可编辑名称、备注、连接密码)后，点击“确定”按钮保存编辑的信息,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户修改物理密码机名称、备注或连接密码并保存,更新物理密码机信息,读取当前物理密码机信息,R,物理密码机信息,名称、备注、连接密码,1,,,,
,,,,,,,,输入修改后的信息,E,修改信息,名称、备注、连接密码,1,,,,
,,,,,,,,更新数据库记录,W,物理密码机信息,名称、备注、连接密码,1,,,,
,,物理密码机删除,在密码机信息列表中可以删除已经注册到系统中的设备信息。,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户确认删除物理密码机,删除物理密码机注册信息,输入物理密码机ID,E,物理密码机标识,物理密码机ID,1,,,,
,,,,,,,,检查设备是否在线及是否被引用,R,设备状态与引用信息,在线状态、服务绑定,1,,,,
,,,,,,,,删除数据库中的设备记录,W,物理密码机信息,物理密码机ID,1,,,,
,,物理密码机详情,在密码机列表页，点击右侧操作列“详情”按钮，系统打开密码机详情页面。,5.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户点击“详情”按钮,查看物理密码机详细信息,输入物理密码机ID,E,物理密码机标识,物理密码机ID,1,,,,
,,,,,,,,读取完整物理密码机信息,R,物理密码机信息,名称、厂商、设备类型、管理IP、端口、序列号、版本、完整性校验、备注,1,,,,
,,,,,,,,输出详情页面,X,物理密码机详情,所有字段信息,1,,,,
,,强制删除,解决物理密码机已损坏，无法正常删除情况,3.0,发起者：系统管理员，接收者：密码资产数据管理系统,用户对已损坏的物理密码机执行强制删除,清除系统中失效的物理密码机记录,输入物理密码机ID,E,物理密码机标识,物理密码机ID,1,,,,
,,,,,,,,验证设备无法通信,R,连接状态,心跳超时、认证失败,1,,,,
,,,,,,,,强制删除数据库记录,W,物理密码机信息,物理密码机ID,1,,,,
,,管理页面跳转,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3.0,发起者：系统管理员，接收者：物理/虚拟密码机管理页面,用户点击“跳转管理页面”按钮,跳转至设备原生管理界面,读取设备管理页面URL,R,管理页面地址,URL、认证方式,1,,,,
,,,,,,,,发起重定向请求,X,跳转指令,目标URL,1,,,,
,,保护主密钥创建,服务器密码机、虚拟服务器密码机、签名验签服务器的保护主密钥的创建,5.0,发起者：系统管理员，接收者：密码设备,用户执行创建保护主密钥操作,在密码设备中生成保护主密钥,选择目标密码设备,E,设备标识,设备ID、类型,1,,,,
,,,,,,,,发送主密钥生成指令,W,密钥生成指令,命令类型、设备ID,1,,,,
,,,,,,,,接收密钥生成结果,R,密钥状态,生成状态、密钥ID,1,,,,
,,,,,,,,记录密钥信息,W,保护主密钥信息,密钥ID、设备ID、创建时间,1,,,,
,,保护主密钥同步,支持设备内保护主密钥的同步,4.0,发起者：系统管理员，接收者：密码设备集群,用户执行密钥同步操作,同步设备间保护主密钥,选择源设备和目标设备,E,设备对,源设备ID、目标设备ID,1,,,,
,,,,,,,,读取源设备主密钥,R,保护主密钥,密钥数据、密钥ID,1,,,,
,,,,,,,,发送密钥同步指令,W,同步任务,源、目标、密钥ID,1,,,,
,,,,,,,,返回同步结果,X,同步结果,成功/失败,1,,,,
,,保护主密钥备份,将设备内保护主密钥的加密导出备份,3.0,发起者：系统管理员，接收者：备份存储系统,用户执行保护主密钥备份操作,导出并加密备份保护主密钥,选择要备份的密钥,E,密钥标识,密钥ID,1,,,,
,,,,,,,,读取密钥数据,R,保护主密钥,密钥内容,1,,,,
,,,,,,,,加密并导出密钥文件,W,密钥备份文件,加密文件、校验码,1,,,,
,,保护主密钥还原,还原设备内保护主密钥,3.0,发起者：系统管理员，接收者：密码设备,用户上传密钥备份文件并执行还原,将备份的保护主密钥还原至设备,上传密钥备份文件,E,密钥备份文件,加密文件,1,,,,
,,,,,,,,解密并验证密钥文件,R,密钥文件内容,解密数据、校验码,1,,,,
,,,,,,,,将密钥写入目标设备,W,保护主密钥,密钥内容、设备ID,1,,,,
