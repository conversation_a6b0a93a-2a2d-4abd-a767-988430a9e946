#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证main.py能否正常导入和运行
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # 尝试导入main.py中的函数
    from main import extract_json_from_content, flatten_results_with_subprocess
    print("✅ 成功导入main.py中的函数")
    
    # 测试extract_json_from_content函数
    test_content = '''
    这是一些文本
    ```json
    [{"test": "value"}]
    ```
    更多文本
    '''
    
    result = extract_json_from_content(test_content)
    if result == [{"test": "value"}]:
        print("✅ extract_json_from_content函数工作正常")
    else:
        print("❌ extract_json_from_content函数返回结果不正确")
        print(f"期望: [{'test': 'value'}]")
        print(f"实际: {result}")
    
    # 测试flatten_results_with_subprocess函数
    test_data = [
        {
            "模块": "测试模块",
            "子过程": [
                {"子过程描述": "步骤1", "CFP": 1},
                {"子过程描述": "步骤2", "CFP": 1}
            ]
        }
    ]
    
    flat_result = flatten_results_with_subprocess(test_data)
    if len(flat_result) == 2 and flat_result[0]["子过程描述"] == "步骤1":
        print("✅ flatten_results_with_subprocess函数工作正常")
    else:
        print("❌ flatten_results_with_subprocess函数返回结果不正确")
        print(f"结果: {flat_result}")
    
    print("\n🎉 所有验证通过！main.py修改成功！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
